<form id="login_form" action="{{ href("/login") }}" method="post">
    <div id="errors"></div>
    <div class="mb-3">
        <label class="form-label">{{ "账号"|trans }}</label>
        <input name="login" type="text" class="form-control" placeholder="{{ "请输入账号"|trans }}"/>
        <div name="login" class="invalid-feedback"></div>
    </div>
    <div class="mb-3">
        <label class="form-label">
            {{ "密码"|trans }}
            <span class="form-label-description">
                 <a href="{{ request.contextPath }}/forgot-password.html" tabindex="-1">{{ "重置密码"|trans }}</a>
            </span>
        </label>
        <input class="form-control" name="password" type="password" placeholder="{{ "请输入密码"|trans }}"/>
        <div class="invalid-feedback" name="password"></div>
    </div>
    <div class="mb-3">
        <div class="row">
            <label class="form-label">{{ "验证码"|trans }}</label>
            <div class="col-9">
                <input name="captcha" value="{{  g_is_dev ? '1234' : '' }}" type="text" class="form-control" placeholder="{{ "验证码"|trans }}"/>
                <div name="captcha" class="invalid-feedback"></div>
            </div>
            <div class="col-3">
                <img id="captchaImage" style="height: 36px;" src="{{ href("/security/captcha") }}?{{ random() }}">
            </div>
        </div>
    </div>
    <div class="mb-2">
        <label class="form-check"><input name="rememberMe" type="checkbox" value="true" class="form-check-input"/>
            <span class="form-check-label">{{ "记住我"|trans }}</span>
        </label>
    </div>
    <div class="form-footer">
        <button id="btn_login" type="button" class="btn btn-primary w-100">{{ "登录"|trans }}</button>
    </div>
</form>

<script defer>
    if(window !== window.top) {
        window.top.location.href = window.location.href;
    }

    function refreshCaptcha() {
        let src = "{{ href("/security/captcha") }}?" + Math.random();
        $("#captchaImage").attr('src', src);
    }
    $(function () {
        let isLogging = false;

        $("#captchaImage").click(() => {
            refreshCaptcha();
        });

        function clearErrors() {
            $("#login_form input").removeClass('is-invalid');
            $("#login_form .invalid-feedback").text('');
            $(".toast").remove();
        }

        function showToast(message, type = 'danger') {
            const toastHtml = `
                <div class="toast show" role="alert" data-bs-autohide="true" data-bs-delay="5000">
                    <div class="toast-header text-${type}">
                        <strong class="me-auto">系统提示</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">${message}</div>
                </div>
            `;

            let container = $('.toast-container');
            if (container.length === 0) {
                container = $('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
                $('body').append(container);
            }

            const $toast = $(toastHtml);
            container.append($toast);

            setTimeout(() => $toast.remove(), 5000);
        }

        async function handleLogin(e) {
            if (isLogging) return;

            clearErrors();
            isLogging = true;

            const $loginBtn = $("#btn_login");
            $loginBtn.prop('disabled', true).addClass('btn-loading');

            try {
                const res = await $.axios.post('/api/login', document.querySelector('#login_form'));
                const {data} = res;

                if (data.succeed) {
                    return $.utils.redirect("/home");
                }

                refreshCaptcha();

                if (data.error && typeof data.error === 'object') {
                    for (let key in data.error) {
                        const $input = $(`input[name=${key}]`);
                        $input.addClass('is-invalid');
                        $input.siblings(`div[name="${key}"]`).text(data.error[key]);
                    }
                } else {
                    showToast(data.msg || "登录失败，请重试");
                }
            } catch (e) {
                refreshCaptcha();
                showToast("登录失败，请重试");
            } finally {
                $loginBtn.prop('disabled', false).removeClass('btn-loading');
                isLogging = false;
            }
        }

        $("#btn_login").click(handleLogin);

        $("#login_form input").keypress(function (e) {
            if (e.which === 13) {
                e.preventDefault();
                handleLogin(e);
            }
        });
    });
</script>
