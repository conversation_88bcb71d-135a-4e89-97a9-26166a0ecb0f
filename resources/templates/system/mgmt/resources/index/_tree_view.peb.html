<div class="toast-container position-fixed top-0 start-50 translate-middle-x p-3" id="toast-container"
     style="z-index: 9999;"></div>
<div class="card-body p-0">
    <div class="resource-tree" id="resource-tree-container">
        <div class="d-flex justify-content-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    </div>
</div>


{# 删除确认模态框 #}
<div aria-hidden="true" class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h6 class="modal-title mb-0">
                    <i class="ti ti-alert-triangle text-danger me-2"></i>
                    确认删除
                </h6>
                <button aria-label="Close" class="btn-close" data-bs-dismiss="modal" type="button"></button>
            </div>
            <div class="modal-body py-2">
                <div class="text-center">
                    <div class="mb-2">
                        <i class="ti ti-trash text-danger" style="font-size: 2rem;"></i>
                    </div>
                    <h6>确定要删除资源吗？</h6>
                    <p class="text-muted mb-0 small">
                        资源 "<span class="fw-bold" id="deleteResourceName"></span>" 将被永久删除
                    </p>
                    <div class="alert alert-warning py-1 mt-2 text-start small" id="deleteWarning"
                         style="display: none;">
                        <i class="ti ti-alert-triangle me-1"></i>
                        <span id="deleteWarningText"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button class="btn btn-secondary btn-sm" data-bs-dismiss="modal" type="button">取消</button>
                <button class="btn btn-danger btn-sm" id="confirmDeleteBtn" type="button">确认删除</button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        let resourceTree = [];
        let filteredTree = [];
        let expandedNodes = new Set();
        let currentFilter = {};

        loadResourceTree();

        $(document).on('filterResourceTree', function (e, filterData) {
            currentFilter = filterData;
            applyFilter();
        });

        $('#btn-create-resource').click(() => {
            // 直接触发模态框的显示，让模态框自己处理创建逻辑
            $('#resourceEditModal').modal('show');
        });

        $('#btn-expand-all').click(() => {
            expandAllNodes();
        });

        $('#btn-collapse-all').click(() => {
            collapseAllNodes();
        });

        function loadResourceTree() {
            $.get('/admiz/api/system/mgmt/resources/tree')
                .done(function (result) {
                    if (result.succeed) {
                        resourceTree = result.data;
                        filteredTree = resourceTree;
                        renderResourceTree();
                    } else {
                        window.showToast('加载资源树失败: ' + result.msg, 'danger');
                    }
                })
                .fail(function () {
                    window.showToast('加载资源树失败，请稍后重试', 'danger');
                });
        }

        function renderResourceTree() {
            const container = $('#resource-tree-container');
            container.empty();

            if (filteredTree.length === 0) {
                container.html(`
                <div class="empty">
                    <div class="empty-icon">
                        <i class="ti ti-folder-x"></i>
                    </div>
                    <p class="empty-title">暂无资源</p>
                    <p class="empty-subtitle text-secondary">点击"创建资源"按钮添加第一个资源</p>
                </div>
            `);
                return;
            }

            const treeHtml = filteredTree.map(resource => renderResourceNode(resource, 0)).join('');
            container.html(treeHtml);

            bindTreeEvents();
        }

        function renderResourceNode(resource, level) {
            const hasChildren = resource.children && resource.children.length > 0;
            const isExpanded = expandedNodes.has(resource.id);
            const indent = level * 24;

            const typeIcon = getResourceTypeIcon(resource.resourceType);
            const statusBadge = getStatusBadge(resource);

            let html = `
            <div class="resource-node" data-resource-id="${resource.id}" data-level="${level}" data-resource-type="${resource.resourceType}">
                <div class="d-flex align-items-center py-2 px-3 border-bottom resource-item" style="padding-left: ${indent + 12}px !important;">
                    <div class="resource-toggle me-2" style="width: 16px;">
                        ${hasChildren ? `<i class="ti ti-chevron-${isExpanded ? 'down' : 'right'} cursor-pointer text-muted"></i>` : '<span style="width: 12px; display: inline-block;"></span>'}
                    </div>
                    <div class="resource-icon me-2">
                        ${typeIcon}
                    </div>
                    <div class="flex-fill">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center flex-wrap">
                                <strong class="resource-name me-2">${resource.displayName || resource.name}</strong>
                                ${statusBadge}
                                <span class="badge bg-secondary-lt text-secondary ms-1 small">${getResourceTypeText(resource.resourceType)}</span>
                                ${hasChildren ? `<span class="badge bg-blue-lt text-blue ms-1 small">${resource.children.length}</span>` : ''}
                            </div>
                            <div class="resource-actions">
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-primary btn-edit-resource"
                                            data-resource-id="${resource.id}"
                                            data-bs-toggle="modal"
                                            data-bs-target="#resourceEditModal"
                                            title="编辑">
                                        <i class="ti ti-edit"></i> 编辑
                                    </button>
                                    <button class="btn btn-outline-success btn-add-child"
                                            data-parent-id="${resource.id}"
                                            data-parent-name="${resource.displayName || resource.name}"
                                            title="添加子项">
                                        <i class="ti ti-plus"></i> 添加
                                    </button>
                                    <button class="btn btn-outline-danger btn-delete-resource"
                                            data-resource-id="${resource.id}"
                                            data-resource-name="${resource.displayName || resource.name}"
                                            title="删除">
                                        <i class="ti ti-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="text-secondary small mt-1">
                            <div class="d-flex flex-wrap gap-2">
                                <span><i class="ti ti-key me-1"></i><code class="small">${resource.permission}</code></span>
                                ${resource.url ? `<span><i class="ti ti-link me-1"></i><code class="small">${resource.url}</code></span>` : ''}
                                <span><i class="ti ti-sort-ascending me-1"></i>${resource.sortNum}</span>
                            </div>
                            ${resource.remark ? `<div class="mt-1 text-muted small"><i class="ti ti-note me-1"></i>${resource.remark}</div>` : ''}
                        </div>
                    </div>
                </div>
        `;

            if (hasChildren && isExpanded) {
                html += '<div class="resource-children">';
                resource.children.forEach(child => {
                    html += renderResourceNode(child, level + 1);
                });
                html += '</div>';
            }

            html += '</div>';
            return html;
        }

        function getResourceTypeIcon(resourceType) {
            const icons = {
                'FOLDER': '<i class="ti ti-folder text-warning"></i>',
                'MENU': '<i class="ti ti-menu-2 text-primary"></i>',
                'FUNCTION': '<i class="ti ti-tool text-info"></i>',
                'DATA': '<i class="ti ti-database text-success"></i>'
            };
            return icons[resourceType] || '<i class="ti ti-file"></i>';
        }

        function getStatusBadge(resource) {
            if (resource.disabled) {
                return '<span class="badge bg-red-lt text-red ms-2">已禁用</span>';
            }
            if (resource.hidden) {
                return '<span class="badge bg-yellow-lt text-yellow ms-2">已隐藏</span>';
            }
            return '<span class="badge bg-green-lt text-green ms-2">正常</span>';
        }

        function getResourceTypeText(resourceType) {
            const typeTexts = {
                'FOLDER': '目录',
                'MENU': '菜单',
                'FUNCTION': '功能',
                'DATA': '数据'
            };
            return typeTexts[resourceType] || resourceType;
        }

        function bindTreeEvents() {
            $('.resource-toggle i').click(function (e) {
                e.stopPropagation();
                const resourceNode = $(this).closest('.resource-node');
                const resourceId = parseInt(resourceNode.data('resource-id'));
                toggleNode(resourceId);
            });

            $('.btn-edit-resource').click(function (e) {
                e.stopPropagation();
                // 新的编辑模态框会通过data-bs-target自动打开
            });

            $('.btn-add-child').click(function (e) {
                e.stopPropagation();
                const parentId = $(this).data('parent-id');
                const parentName = $(this).data('parent-name');

                // 打开创建模态框并预设父级
                $('#resourceEditModal').modal('show');

                // 设置父级资源
                setTimeout(() => {
                    document.getElementById('editResourceParentId').value = parentId;
                    document.getElementById('modalTitle').textContent = `在 "${parentName}" 下创建子资源`;
                }, 100);
            });

            $('.btn-delete-resource').click(function (e) {
                e.stopPropagation();
                const resourceId = $(this).data('resource-id');
                const resourceName = $(this).data('resource-name');

                showDeleteConfirmModal(resourceId, resourceName);
            });

            // 资源项点击展开/收起
            $('.resource-item').click(function (e) {
                if ($(e.target).closest('.resource-actions, .btn').length === 0) {
                    const resourceNode = $(this).closest('.resource-node');
                    const resourceId = parseInt(resourceNode.data('resource-id'));
                    const hasChildren = resourceNode.find('.resource-children').length > 0 ||
                        resourceNode.find('.resource-toggle i').length > 0;

                    if (hasChildren) {
                        toggleNode(resourceId);
                    }
                }
            });
        }

        function toggleNode(resourceId) {
            if (expandedNodes.has(resourceId)) {
                expandedNodes.delete(resourceId);
            } else {
                expandedNodes.add(resourceId);
            }
            renderResourceTree();
        }

        function expandAllNodes() {
            function addAllIds(resources) {
                resources.forEach(resource => {
                    if (resource.children && resource.children.length > 0) {
                        expandedNodes.add(resource.id);
                        addAllIds(resource.children);
                    }
                });
            }

            addAllIds(filteredTree);
            renderResourceTree();
        }

        function applyFilter() {
            const hasAnyFilter = currentFilter.searchText ||
                currentFilter.resourceType ||
                currentFilter.disabled !== '' ||
                currentFilter.levelFilter ||
                currentFilter.hasChildrenFilter ||
                currentFilter.hiddenFilter;

            if (!hasAnyFilter) {
                filteredTree = resourceTree;
            } else {
                filteredTree = filterTreeNodes(resourceTree, 0);
            }
            renderResourceTree();
        }

        function filterTreeNodes(nodes, currentLevel = 0) {
            return nodes.filter(node => {
                const matchesFilter = matchesCurrentFilter(node, currentLevel);
                const hasMatchingChildren = node.children && filterTreeNodes(node.children, currentLevel + 1).length > 0;

                if (matchesFilter || hasMatchingChildren) {
                    const filteredNode = {...node};
                    if (node.children) {
                        filteredNode.children = filterTreeNodes(node.children, currentLevel + 1);
                    }
                    return filteredNode;
                }
                return null;
            }).filter(node => node !== null);
        }

        function matchesCurrentFilter(node, currentLevel) {
            // 文本搜索
            if (currentFilter.searchText) {
                const searchText = currentFilter.searchText.toLowerCase();
                const nameMatch = (node.name || '').toLowerCase().includes(searchText);
                const displayNameMatch = (node.displayName || '').toLowerCase().includes(searchText);
                const permissionMatch = (node.permission || '').toLowerCase().includes(searchText);
                const urlMatch = (node.url || '').toLowerCase().includes(searchText);

                if (!nameMatch && !displayNameMatch && !permissionMatch && !urlMatch) {
                    return false;
                }
            }

            // 资源类型过滤
            if (currentFilter.resourceType && node.resourceType !== currentFilter.resourceType) {
                return false;
            }

            // 状态过滤
            if (currentFilter.disabled !== '') {
                const isDisabled = currentFilter.disabled === 'true';
                if (node.disabled !== isDisabled) {
                    return false;
                }
            }

            // 层级过滤
            if (currentFilter.levelFilter !== '') {
                const targetLevel = parseInt(currentFilter.levelFilter);
                if (targetLevel === 3) {
                    // 第四层及以下
                    if (currentLevel < 3) {
                        return false;
                    }
                } else {
                    if (currentLevel !== targetLevel) {
                        return false;
                    }
                }
            }

            // 子项过滤
            if (currentFilter.hasChildrenFilter) {
                const hasChildren = node.children && node.children.length > 0;
                if (!hasChildren) {
                    return false;
                }
            }

            // 隐藏资源过滤
            if (!currentFilter.hiddenFilter && node.hidden) {
                return false;
            }

            return true;
        }

        function collapseAllNodes() {
            expandedNodes.clear();
            renderResourceTree();
        }

        function showDeleteConfirmModal(resourceId, resourceName) {
            const resourceNameSpan = document.getElementById('deleteResourceName');
            const warningDiv = document.getElementById('deleteWarning');
            const warningText = document.getElementById('deleteWarningText');
            const confirmBtn = document.getElementById('confirmDeleteBtn');

            resourceNameSpan.textContent = resourceName;

            // 检查是否有子资源
            const resourceNode = $(`.resource-node[data-resource-id="${resourceId}"]`);
            const hasChildren = resourceNode.find('.resource-children .resource-node').length > 0;

            if (hasChildren) {
                warningDiv.style.display = 'block';
                warningText.textContent = '此资源包含子资源，删除后子资源也将被删除！';
                confirmBtn.className = 'btn btn-danger';
                confirmBtn.innerHTML = '<i class="ti ti-trash me-1"></i>强制删除';
            } else {
                warningDiv.style.display = 'none';
                confirmBtn.className = 'btn btn-danger';
                confirmBtn.innerHTML = '<i class="ti ti-trash me-1"></i>确认删除';
            }

            confirmBtn.onclick = function () {
                deleteResource(resourceId);
                $('#deleteConfirmModal').modal('hide');
            };

            $('#deleteConfirmModal').modal('show');
        }

        function deleteResource(resourceId) {
            $.ajax({
                url: `/admiz/api/system/mgmt/resources/${resourceId}`,
                method: 'DELETE'
            })
                .done((result) => {
                    if (result.succeed) {
                        window.showToast(result.msg || '删除成功', 'success');
                        loadResourceTree();
                    } else {
                        window.showToast('删除失败: ' + result.msg, 'danger');
                    }
                })
                .fail((xhr) => {
                    window.showToast('删除失败，请稍后重试', 'danger');
                });
        }






    });

    // 全局函数，供 onclick 事件调用
    window.showToast = function (message, type = 'success') {
        $('#toast-container').showToast(message, type).autoDismiss();
    };


</script>

<style>
    .resource-tree {
        max-height: 80vh;
        overflow-y: auto;
        border: 1px solid var(--tblr-border-color);
        border-radius: var(--tblr-border-radius);
    }

    .resource-item {
        transition: all 0.2s ease;
        cursor: pointer;
        position: relative;
    }

    .resource-item:hover {
        background-color: var(--tblr-bg-surface-secondary);
        border-left: 3px solid var(--tblr-primary);
    }

    .resource-item:last-child {
        border-bottom: none;
    }

    .resource-toggle {
        cursor: pointer;
        transition: transform 0.2s ease;
    }

    .resource-toggle:hover i {
        color: var(--tblr-primary) !important;
        transform: scale(1.1);
    }

    .resource-actions {
        opacity: 0.8;
        transition: opacity 0.2s ease;
    }

    .resource-item:hover .resource-actions {
        opacity: 1;
    }

    .resource-actions .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border-radius: var(--tblr-border-radius);
        margin-left: 0.25rem;
    }

    .resource-actions .btn:first-child {
        margin-left: 0;
    }

    .resource-node[data-level="0"] > .resource-item {
        background-color: var(--tblr-bg-surface);
        font-weight: 500;
    }

    .resource-node[data-level="1"] > .resource-item {
        background-color: var(--tblr-bg-surface-secondary);
    }

    .resource-node[data-level="2"] > .resource-item {
        background-color: var(--tblr-bg-surface-tertiary);
    }

    .resource-children {
        border-left: 2px solid var(--tblr-border-color-light);
        margin-left: 16px;
    }

    .resource-name {
        color: var(--tblr-body-color);
    }

    .resource-node[data-resource-type="FOLDER"] .resource-name {
        color: var(--tblr-warning);
    }

    .resource-node[data-resource-type="MENU"] .resource-name {
        color: var(--tblr-primary);
    }

    .resource-node[data-resource-type="FUNCTION"] .resource-name {
        color: var(--tblr-info);
    }

    .resource-node[data-resource-type="DATA"] .resource-name {
        color: var(--tblr-success);
    }

    .cursor-pointer {
        cursor: pointer;
    }

    /* 空状态样式 */
    .empty {
        padding: 3rem 1rem;
        text-align: center;
    }

    .empty-icon {
        font-size: 4rem;
        color: var(--tblr-text-muted);
        margin-bottom: 1rem;
    }

    /* 加载状态 */
    .resource-tree .spinner-border {
        width: 2rem;
        height: 2rem;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .resource-actions .btn {
            padding: 0.125rem 0.25rem;
            font-size: 0.7rem;
        }

        .resource-actions .btn i {
            font-size: 0.8rem;
        }

        .resource-item {
            padding: 0.75rem !important;
        }
    }
</style>
