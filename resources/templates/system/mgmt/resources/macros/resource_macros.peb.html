{# 资源管理相关的宏定义 #}

{# 资源类型图标宏 #}
{% macro resourceTypeIcon(resourceType) %}
{% if resourceType == 'FOLDER' %}
<i class="ti ti-folder text-warning"></i>
{% elseif resourceType == 'MENU' %}
<i class="ti ti-menu-2 text-primary"></i>
{% elseif resourceType == 'FUNCTION' %}
<i class="ti ti-tool text-info"></i>
{% elseif resourceType == 'DATA' %}
<i class="ti ti-database text-success"></i>
{% else %}
<i class="ti ti-file"></i>
{% endif %}
{% endmacro %}

{# 资源类型文本宏 #}
{% macro resourceTypeText(resourceType) %}
{% if resourceType == 'FOLDER' %}目录
{% elseif resourceType == 'MENU' %}菜单
{% elseif resourceType == 'FUNCTION' %}功能
{% elseif resourceType == 'DATA' %}数据
{% else %}{{ resourceType }}
{% endif %}
{% endmacro %}

{# 资源状态徽章宏 #}
{% macro statusBadge(resource) %}
{% if resource.disabled %}
<span class="badge bg-red-lt text-red">已禁用</span>
{% elseif resource.hidden %}
<span class="badge bg-yellow-lt text-yellow">已隐藏</span>
{% else %}
<span class="badge bg-green-lt text-green">正常</span>
{% endif %}
{% endmacro %}

{# 基础表单字段宏 #}
{% macro baseFormFields(resource, parentResources) %}
<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="resourceName">资源名称 <span class="text-danger">*</span></label>
        <input class="form-control" id="resourceName" name="name" required
               type="text" value="{{ resource.name | default('') }}">
        <div class="invalid-feedback"></div>
    </div>
    <div class="col-md-6">
        <label class="form-label" for="resourceDisplayName">显示名称 <span class="text-danger">*</span></label>
        <input class="form-control" id="resourceDisplayName" name="displayName" required
               type="text" value="{{ resource.displayName | default('') }}">
        <div class="invalid-feedback"></div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="resourcePermission">权限编码 <span class="text-danger">*</span></label>
        <input class="form-control" id="resourcePermission" name="permission" required
               type="text" value="{{ resource.permission | default('') }}">
        <div class="invalid-feedback"></div>
    </div>
    <div class="col-md-6">
        <label class="form-label" for="resourceType">资源类型 <span class="text-danger">*</span></label>
        <select class="form-select" id="resourceType" name="resourceType" required>
            <option value="">请选择资源类型</option>
            <option if resource.resourceType== value="FOLDER" {%
            'FOLDER' %}selected{% endif %}>目录</option>
            <option if resource.resourceType== value="MENU" {%
            'MENU' %}selected{% endif %}>菜单</option>
            <option if resource.resourceType== value="FUNCTION" {%
            'FUNCTION' %}selected{% endif %}>功能</option>
            <option if resource.resourceType== value="DATA" {%
            'DATA' %}selected{% endif %}>数据</option>
        </select>
        <div class="invalid-feedback"></div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="resourceParentId">父级资源</label>
        <select class="form-select" id="resourceParentId" name="parentId">
            <option value="">无父级（顶级资源）</option>
            {% for parent in parentResources %}
            <option %}
                    %}selected{% endif if parent.id resource.parentId== value="{{ parent.id }}" {%>
                {{ parent.displayName | default(parent.name) }}
            </option>
            {% endfor %}
        </select>
    </div>
    <div class="col-md-6">
        <label class="form-label" for="resourceSortNum">排序</label>
        <input class="form-control" id="resourceSortNum" name="sortNum" type="number"
               value="{{ resource.sortNum | default(100) }}">
    </div>
</div>
{% endmacro %}

{# 状态选项宏 #}
{% macro statusOptions(resource) %}
<div class="row mb-3">
    <div class="col-md-12">
        <label class="form-label">状态选项</label>
        <div>
            <label class="form-check form-check-inline">
                <input %} %}checked{% class="form-check-input" endif
                       id="resourceDisabled" if name="disabled" resource.disabled type="checkbox" {%>
                <span class="form-check-label">禁用</span>
            </label>
            <label class="form-check form-check-inline">
                <input %} %}checked{% class="form-check-input" endif
                       id="resourceHidden" if name="hidden" resource.hidden type="checkbox" {%>
                <span class="form-check-label">隐藏</span>
            </label>
            <label class="form-check form-check-inline">
                <input %} %}checked{% class="form-check-input" endif
                       id="resourceAutoRefresh" if name="autoRefresh" resource.autoRefresh type="checkbox" {%>
                <span class="form-check-label">自动刷新</span>
            </label>
        </div>
    </div>
</div>
{% endmacro %}

{# 备注字段宏 #}
{% macro remarkField(resource) %}
<div class="mb-3">
    <label class="form-label" for="resourceRemark">备注</label>
    <textarea class="form-control" id="resourceRemark" name="remark" placeholder="资源描述或备注信息"
              rows="3">{{ resource.remark | default('') }}</textarea>
</div>
{% endmacro %}
