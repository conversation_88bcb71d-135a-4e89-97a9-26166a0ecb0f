{# 功能类型资源专用字段 #}

<div class="alert alert-info">
    <div class="d-flex">
        <div>
            <i class="ti ti-tool"></i>
        </div>
        <div class="ms-2">
            <h4 class="alert-title">功能资源配置</h4>
            <div class="text-secondary">功能资源用于控制具体的操作权限，如增删改查等。</div>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="functionAction">功能操作 <span class="text-danger">*</span></label>
        <select class="form-select" id="functionAction" name="action" required>
            <option value="">请选择操作类型</option>
            <option if resource.action== value="create" {%
            'create' %}selected{% endif %}>创建</option>
            <option if resource.action== value="read" {%
            'read' %}selected{% endif %}>查看</option>
            <option if resource.action== value="update" {%
            'update' %}selected{% endif %}>更新</option>
            <option if resource.action== value="delete" {%
            'delete' %}selected{% endif %}>删除</option>
            <option if resource.action== value="export" {%
            'export' %}selected{% endif %}>导出</option>
            <option if resource.action== value="import" {%
            'import' %}selected{% endif %}>导入</option>
            <option if resource.action== value="approve" {%
            'approve' %}selected{% endif %}>审批</option>
            <option if resource.action== value="custom" {%
            'custom' %}selected{% endif %}>自定义</option>
        </select>
        <div class="form-text">定义此功能的具体操作类型</div>
    </div>
    <div class="col-md-6">
        <label class="form-label" for="functionMethod">HTTP方法</label>
        <select class="form-select" id="functionMethod" name="httpMethod">
            <option %} %}selected{% endif if not resource.httpMethod value="" {%>不限制</option>
            <option if resource.httpMethod== value="GET" {%
            'GET' %}selected{% endif %}>GET</option>
            <option if resource.httpMethod== value="POST" {%
            'POST' %}selected{% endif %}>POST</option>
            <option if resource.httpMethod== value="PUT" {%
            'PUT' %}selected{% endif %}>PUT</option>
            <option if resource.httpMethod== value="DELETE" {%
            'DELETE' %}selected{% endif %}>DELETE</option>
            <option if resource.httpMethod== value="PATCH" {%
            'PATCH' %}selected{% endif %}>PATCH</option>
        </select>
        <div class="form-text">限制此功能允许的HTTP请求方法</div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="functionUrl">API端点</label>
        <input class="form-control" id="functionUrl" name="url" placeholder="/api/system/users"
               type="text"
               value="{{ resource.url | default('') }}">
        <div class="form-text">此功能对应的API接口地址</div>
    </div>
    <div class="col-md-6">
        <label class="form-label" for="functionIcon">功能图标</label>
        <div class="input-group">
            <input class="form-control" id="functionIcon" name="icon" placeholder="ti ti-tool"
                   type="text"
                   value="{{ resource.icon | default('ti ti-tool') }}">
            <button class="btn btn-outline-secondary" id="functionIconPreview" type="button">
                <i class="{{ resource.icon | default('ti ti-tool') }}"></i>
            </button>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">权限控制</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-check">
                            <input checked class="form-check-input" id="functionRequireAuth" type="checkbox">
                            <span class="form-check-label">需要认证</span>
                        </label>
                        <div class="form-text">执行此功能需要用户登录</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-check">
                            <input class="form-check-input" id="functionAuditLog" type="checkbox">
                            <span class="form-check-label">记录审计日志</span>
                        </label>
                        <div class="form-text">记录此功能的执行日志</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-check">
                            <input class="form-check-input" id="functionRateLimit" type="checkbox">
                            <span class="form-check-label">频率限制</span>
                        </label>
                        <div class="form-text">限制此功能的调用频率</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-3" id="rateLimitConfig" style="display: none;">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">频率限制配置</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label" for="rateLimit">限制次数</label>
                        <input class="form-control" id="rateLimit" min="1" name="rateLimit"
                               type="number" value="100">
                        <div class="form-text">时间窗口内允许的最大调用次数</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label" for="rateLimitWindow">时间窗口（秒）</label>
                        <input class="form-control" id="rateLimitWindow" min="1" name="rateLimitWindow"
                               type="number" value="60">
                        <div class="form-text">频率限制的时间窗口</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label" for="rateLimitScope">限制范围</label>
                        <select class="form-select" id="rateLimitScope" name="rateLimitScope">
                            <option value="user">按用户</option>
                            <option value="ip">按IP</option>
                            <option value="global">全局</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 图标预览功能
        const iconInput = document.getElementById('functionIcon');
        const iconPreview = document.getElementById('functionIconPreview');

        if (iconInput && iconPreview) {
            iconInput.addEventListener('input', function () {
                const iconClass = this.value || 'ti ti-tool';
                iconPreview.innerHTML = `<i class="${iconClass}"></i>`;
            });
        }

        // 频率限制配置显示/隐藏
        const rateLimitCheckbox = document.getElementById('functionRateLimit');
        const rateLimitConfig = document.getElementById('rateLimitConfig');

        if (rateLimitCheckbox && rateLimitConfig) {
            rateLimitCheckbox.addEventListener('change', function () {
                rateLimitConfig.style.display = this.checked ? 'block' : 'none';
            });
        }

        // 根据操作类型自动设置HTTP方法
        const actionSelect = document.getElementById('functionAction');
        const methodSelect = document.getElementById('functionMethod');

        if (actionSelect && methodSelect) {
            actionSelect.addEventListener('change', function () {
                const action = this.value;
                const methodMap = {
                    'create': 'POST',
                    'read': 'GET',
                    'update': 'PUT',
                    'delete': 'DELETE',
                    'export': 'GET',
                    'import': 'POST',
                    'approve': 'PUT'
                };

                if (methodMap[action]) {
                    methodSelect.value = methodMap[action];
                }
            });
        }
    });
</script>
