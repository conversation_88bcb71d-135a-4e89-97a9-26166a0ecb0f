{# 数据权限类型资源专用字段 #}

<div class="alert alert-success">
    <div class="d-flex">
        <div>
            <i class="ti ti-database"></i>
        </div>
        <div class="ms-2">
            <h4 class="alert-title">数据权限配置</h4>
            <div class="text-secondary">数据权限用于控制用户对特定数据的访问范围和操作权限。</div>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="dataScope">数据范围 <span class="text-danger">*</span></label>
        <select class="form-select" id="dataScope" name="dataScope" required>
            <option value="">请选择数据范围</option>
            <option if resource.dataScope== value="all" {%
            'all' %}selected{% endif %}>全部数据</option>
            <option if resource.dataScope== value="dept" {%
            'dept' %}selected{% endif %}>本部门数据</option>
            <option if resource.dataScope== value="dept_and_child" {%
            'dept_and_child' %}selected{% endif %}>本部门及子部门数据</option>
            <option if resource.dataScope== value="self" {%
            'self' %}selected{% endif %}>仅本人数据</option>
            <option if resource.dataScope== value="custom" {%
            'custom' %}selected{% endif %}>自定义数据范围</option>
        </select>
        <div class="form-text">定义用户可以访问的数据范围</div>
    </div>
    <div class="col-md-6">
        <label class="form-label" for="dataEntity">数据实体</label>
        <input class="form-control" id="dataEntity" name="dataEntity" placeholder="User, Department, Order"
               type="text"
               value="{{ resource.dataEntity | default('') }}">
        <div class="form-text">此权限控制的数据实体名称</div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">数据操作权限</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-check">
                            <input checked class="form-check-input" id="dataCanRead" type="checkbox">
                            <span class="form-check-label">查看</span>
                        </label>
                        <div class="form-text">可以查看数据</div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-check">
                            <input class="form-check-input" id="dataCanCreate" type="checkbox">
                            <span class="form-check-label">创建</span>
                        </label>
                        <div class="form-text">可以创建新数据</div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-check">
                            <input class="form-check-input" id="dataCanUpdate" type="checkbox">
                            <span class="form-check-label">修改</span>
                        </label>
                        <div class="form-text">可以修改数据</div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-check">
                            <input class="form-check-input" id="dataCanDelete" type="checkbox">
                            <span class="form-check-label">删除</span>
                        </label>
                        <div class="form-text">可以删除数据</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-3" id="customScopeConfig" style="display: none;">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">自定义数据范围</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label" for="dataFilter">数据过滤条件</label>
                        <textarea class="form-control" id="dataFilter" name="dataFilter" placeholder="user_id = #{currentUserId}&#10;dept_id IN (#{userDeptIds})"
                                  rows="4">{{ resource.dataFilter | default('') }}</textarea>
                        <div class="form-text">使用SQL WHERE条件语法，支持变量替换</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="dataColumns">可访问字段</label>
                        <textarea class="form-control" id="dataColumns" name="dataColumns" placeholder="id, name, email&#10;!password, !secret_key"
                                  rows="4">{{ resource.dataColumns | default('') }}</textarea>
                        <div class="form-text">指定可访问的字段，使用!前缀表示排除字段</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">数据安全</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-check">
                            <input class="form-check-input" id="dataEncryption" type="checkbox">
                            <span class="form-check-label">数据加密</span>
                        </label>
                        <div class="form-text">对敏感数据进行加密存储</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-check">
                            <input class="form-check-input" id="dataDesensitization" type="checkbox">
                            <span class="form-check-label">数据脱敏</span>
                        </label>
                        <div class="form-text">对敏感信息进行脱敏处理</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-check">
                            <input checked class="form-check-input" id="dataAuditLog" type="checkbox">
                            <span class="form-check-label">访问审计</span>
                        </label>
                        <div class="form-text">记录数据访问日志</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="dataRetentionDays">数据保留天数</label>
        <input class="form-control" id="dataRetentionDays" min="1" name="dataRetentionDays"
               type="number" value="{{ resource.dataRetentionDays | default(365) }}">
        <div class="form-text">数据的保留期限，超期后可能被清理</div>
    </div>
    <div class="col-md-6">
        <label class="form-label" for="dataBackupLevel">备份级别</label>
        <select class="form-select" id="dataBackupLevel" name="dataBackupLevel">
            <option if resource.dataBackupLevel== value="none" {%
            'none' %}selected{% endif %}>不备份</option>
            <option if resource.dataBackupLevel== value="daily" {%
            'daily' %}selected{% endif %}>每日备份</option>
            <option if resource.dataBackupLevel== value="weekly" {%
            'weekly' %}selected{% endif %}>每周备份</option>
            <option if resource.dataBackupLevel== value="monthly" {%
            'monthly' %}selected{% endif %}>每月备份</option>
            <option if resource.dataBackupLevel== value="realtime" {%
            'realtime' %}selected{% endif %}>实时备份</option>
        </select>
        <div class="form-text">数据的备份策略</div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 数据范围变化时显示/隐藏自定义配置
        const dataScopeSelect = document.getElementById('dataScope');
        const customScopeConfig = document.getElementById('customScopeConfig');

        if (dataScopeSelect && customScopeConfig) {
            dataScopeSelect.addEventListener('change', function () {
                customScopeConfig.style.display = this.value === 'custom' ? 'block' : 'none';
            });

            // 初始化显示状态
            if (dataScopeSelect.value === 'custom') {
                customScopeConfig.style.display = 'block';
            }
        }

        // 数据操作权限联动
        const canRead = document.getElementById('dataCanRead');
        const canCreate = document.getElementById('dataCanCreate');
        const canUpdate = document.getElementById('dataCanUpdate');
        const canDelete = document.getElementById('dataCanDelete');

        if (canRead) {
            canRead.addEventListener('change', function () {
                if (!this.checked) {
                    // 如果不能查看，则不能进行其他操作
                    if (canCreate) canCreate.checked = false;
                    if (canUpdate) canUpdate.checked = false;
                    if (canDelete) canDelete.checked = false;
                }
            });
        }

        // 其他操作权限变化时自动勾选查看权限
        [canCreate, canUpdate, canDelete].forEach(checkbox => {
            if (checkbox) {
                checkbox.addEventListener('change', function () {
                    if (this.checked && canRead) {
                        canRead.checked = true;
                    }
                });
            }
        });
    });
</script>
