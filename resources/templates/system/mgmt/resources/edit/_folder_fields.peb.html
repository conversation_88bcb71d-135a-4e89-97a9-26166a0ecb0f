{# 目录类型资源专用字段 #}

<div class="alert alert-info">
    <div class="d-flex">
        <div>
            <i class="ti ti-info-circle"></i>
        </div>
        <div class="ms-2">
            <h4 class="alert-title">目录资源配置</h4>
            <div class="text-secondary">目录用于组织和分类其他资源，通常作为菜单和功能的容器。</div>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="folderIcon">目录图标</label>
        <div class="input-group">
            <input class="form-control" id="folderIcon" name="icon" placeholder="ti ti-folder"
                   type="text"
                   value="{{ resource.icon | default('ti ti-folder') }}">
            <button class="btn btn-outline-secondary" id="iconPreview" type="button">
                <i class="{{ resource.icon | default('ti ti-folder') }}"></i>
            </button>
        </div>
        <div class="form-text">推荐使用 Tabler Icons，如：ti ti-folder, ti ti-folder-open</div>
    </div>
    <div class="col-md-6">
        <label class="form-label" for="folderUrl">目录链接</label>
        <input class="form-control" id="folderUrl" name="url" placeholder="/path/to/folder"
               type="text"
               value="{{ resource.url | default('') }}">
        <div class="form-text">可选，用于目录点击时的跳转链接</div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">目录特性</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-check">
                            <input %} %}checked{% class="form-check-input"
                                   endif id="folderExpandable" if resource.autoRefresh type="checkbox" {%>
                            <span class="form-check-label">默认展开</span>
                        </label>
                        <div class="form-text">新用户首次访问时是否展开此目录</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-check">
                            <input class="form-check-input" id="folderCollapsible" type="checkbox">
                            <span class="form-check-label">允许收起</span>
                        </label>
                        <div class="form-text">用户是否可以收起此目录</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-check">
                            <input class="form-check-input" id="folderShowCount" type="checkbox">
                            <span class="form-check-label">显示子项数量</span>
                        </label>
                        <div class="form-text">在目录名称后显示子项数量</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 图标预览功能
        const iconInput = document.getElementById('folderIcon');
        const iconPreview = document.getElementById('iconPreview');

        if (iconInput && iconPreview) {
            iconInput.addEventListener('input', function () {
                const iconClass = this.value || 'ti ti-folder';
                iconPreview.innerHTML = `<i class="${iconClass}"></i>`;
            });
        }
    });
</script>
