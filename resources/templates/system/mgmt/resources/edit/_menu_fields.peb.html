{# 菜单类型资源专用字段 #}

<div class="alert alert-primary">
    <div class="d-flex">
        <div>
            <i class="ti ti-menu-2"></i>
        </div>
        <div class="ms-2">
            <h4 class="alert-title">菜单资源配置</h4>
            <div class="text-secondary">菜单用于导航和页面访问，需要配置URL和显示属性。</div>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="menuUrl">菜单URL <span class="text-danger">*</span></label>
        <input class="form-control" id="menuUrl" name="url" placeholder="/system/example"
               required
               type="text" value="{{ resource.url | default('') }}">
        <div class="form-text">菜单点击后跳转的页面地址</div>
    </div>
    <div class="col-md-6">
        <label class="form-label" for="menuTarget">打开方式</label>
        <select class="form-select" id="menuTarget" name="target">
            <option %} %}selected{% endif if not resource.target value="" {%>当前窗口</option>
            <option if resource.target== value="_blank" {%
            '_blank' %}selected{% endif %}>新窗口</option>
            <option if resource.target== value="_self" {%
            '_self' %}selected{% endif %}>当前框架</option>
            <option if resource.target== value="_parent" {%
            '_parent' %}selected{% endif %}>父框架</option>
        </select>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <label class="form-label" for="menuIcon">菜单图标</label>
        <div class="input-group">
            <input class="form-control" id="menuIcon" name="icon" placeholder="ti ti-menu-2"
                   type="text"
                   value="{{ resource.icon | default('ti ti-menu-2') }}">
            <button class="btn btn-outline-secondary" id="menuIconPreview" type="button">
                <i class="{{ resource.icon | default('ti ti-menu-2') }}"></i>
            </button>
        </div>
        <div class="form-text">菜单项前显示的图标</div>
    </div>
    <div class="col-md-6">
        <label class="form-label" for="menuBadge">菜单徽章</label>
        <input class="form-control" id="menuBadge" name="badge" placeholder="NEW"
               type="text"
               value="{{ resource.badge | default('') }}">
        <div class="form-text">可选，在菜单项右侧显示的徽章文本</div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">菜单行为</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-check">
                            <input %} %}checked{% class="form-check-input"
                                   endif id="menuKeepAlive" if resource.autoRefresh type="checkbox" {%>
                            <span class="form-check-label">保持活跃</span>
                        </label>
                        <div class="form-text">页面切换时保持组件状态</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-check">
                            <input class="form-check-input" id="menuCache" type="checkbox">
                            <span class="form-check-label">缓存页面</span>
                        </label>
                        <div class="form-text">缓存页面内容以提高性能</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-check">
                            <input class="form-check-input" id="menuAffix" type="checkbox">
                            <span class="form-check-label">固定标签</span>
                        </label>
                        <div class="form-text">在标签页中固定显示，不可关闭</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">访问控制</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label" for="menuRoles">允许访问的角色</label>
                        <select class="form-select" id="menuRoles" multiple name="allowedRoles">
                            <option value="admin">管理员</option>
                            <option value="user">普通用户</option>
                            <option value="guest">访客</option>
                        </select>
                        <div class="form-text">留空表示所有角色都可访问</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="menuIpWhitelist">IP白名单</label>
                        <textarea class="form-control" id="menuIpWhitelist" name="ipWhitelist" placeholder="***********&#10;10.0.0.0/8"
                                  rows="3"></textarea>
                        <div class="form-text">每行一个IP或IP段，留空表示不限制</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 图标预览功能
        const iconInput = document.getElementById('menuIcon');
        const iconPreview = document.getElementById('menuIconPreview');

        if (iconInput && iconPreview) {
            iconInput.addEventListener('input', function () {
                const iconClass = this.value || 'ti ti-menu-2';
                iconPreview.innerHTML = `<i class="${iconClass}"></i>`;
            });
        }

        // URL验证
        const urlInput = document.getElementById('menuUrl');
        if (urlInput) {
            urlInput.addEventListener('blur', function () {
                const url = this.value.trim();
                if (url && !url.startsWith('/')) {
                    this.value = '/' + url;
                }
            });
        }
    });
</script>
