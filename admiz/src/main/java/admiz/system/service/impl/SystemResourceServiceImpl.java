package admiz.system.service.impl;

import admiz.auth.AuthContext;
import admiz.common.errorhandling.AppErrors;
import admiz.common.errorhandling.ErrorDetailException;
import admiz.system.model.Immutables;
import admiz.system.model.SystemResource;
import admiz.system.model.SystemRole;
import admiz.system.model.dto.CreateSystemResourceInput;
import admiz.system.model.dto.UpdateSystemResourceInput;
import admiz.system.repository.SystemResourceRepository;
import admiz.system.repository.SystemRoleRepository;
import admiz.system.service.SystemResourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.babyfish.jimmer.sql.ast.mutation.SaveMode;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@RequiredArgsConstructor
@Service
public class SystemResourceServiceImpl implements SystemResourceService {

    private final SystemResourceRepository resourceRepository;
    private final SystemRoleRepository roleRepository;

    @Override
    @Transactional
    public SystemResource createResource(@NotNull CreateSystemResourceInput input) {
        // 处理权限编码
        String permission = processPermissionForResource(input);

        // 检查权限编码冲突（如果不为空）
        if (permission != null && !permission.trim().isEmpty() && existsByPermission(permission)) {
            throw AppErrors.CommonError.CONFLICT.toException("权限编码已存在: " + permission);
        }

        if (input.getParentId() != null && !resourceRepository.existsById(input.getParentId())) {
            throw AppErrors.CommonError.NOT_FOUND.toException("父级资源不存在: " + input.getParentId());
        }

        SystemResource resource = Immutables.createSystemResource(draft -> {
            draft.setName(input.getName());
            draft.setDisplayName(input.getDisplayName());
            draft.setParentId(input.getParentId());
            draft.setSortNum(input.getSortNum() != null ? input.getSortNum() : 0);
            draft.setPermission(permission != null ? permission : "");
            draft.setUrl(input.getUrl() != null ? input.getUrl() : "");
            draft.setTarget(input.getTarget() != null ? input.getTarget() : "");
            draft.setResourceType(input.getResourceType());
            draft.setDisabled(input.getDisabled() != null ? input.getDisabled() : false);
            draft.setHidden(input.getHidden() != null ? input.getHidden() : false);
            draft.setAutoRefresh(input.getAutoRefresh() != null ? input.getAutoRefresh() : false);
            draft.setIcon(input.getIcon() != null ? input.getIcon() : "");
            draft.setRemark(input.getRemark() != null ? input.getRemark() : "");
        });

        SystemResource savedResource = resourceRepository.save(resource);

        // 如果是自动生成的权限，分配给当前用户的角色
        if (isExternalResource(input) && permission != null && !permission.trim().isEmpty()) {
            assignPermissionToCurrentUserRoles(savedResource.id(), permission);
        }

        log.info("资源创建成功 - id: {}, name: {}, permission: {}, operator: {}",
                savedResource.id(), savedResource.name(), savedResource.permission(), getCurrentUserInfo());

        return savedResource;
    }

    /**
     * 更新资源
     */
    @Override
    @Transactional
    public SystemResource updateResource(@NotNull UpdateSystemResourceInput input) {
        log.debug("更新资源开始 - id: {}, name: {}, permission: {}",
                input.getId(), input.getName(), input.getPermission());
        
        // 检查资源是否存在
        if (!resourceRepository.existsById(input.getId())) {
            throw AppErrors.CommonError.NOT_FOUND.toException("资源不存在: " + input.getId());
        }
        
        // 检查权限编码是否被其他资源使用（如果权限不为空）
        String permission = input.getPermission();
        if (permission != null && !permission.trim().isEmpty() &&
            existsByPermissionAndIdNot(permission, input.getId())) {
            throw AppErrors.CommonError.CONFLICT.toException("权限编码已被其他资源使用: " + permission);
        }
        
        // 如果指定了父级ID，检查父级是否存在且不是自己
        if (input.getParentId() != null) {
            if (input.getParentId().equals(input.getId())) {
                throw AppErrors.CommonError.FORBIDDEN.toException("不能将自己设置为父级");
            }
            if (!resourceRepository.existsById(input.getParentId())) {
                throw AppErrors.CommonError.NOT_FOUND.toException("父级资源不存在: " + input.getParentId());
            }
        }
        
        SystemResource resource = Immutables.createSystemResource(draft -> {
            draft.setId(input.getId());
            draft.setName(input.getName());
            draft.setDisplayName(input.getDisplayName());
            draft.setParentId(input.getParentId());
            draft.setSortNum(input.getSortNum() != null ? input.getSortNum() : 0);
            draft.setPermission(permission != null ? permission : "");
            draft.setUrl(input.getUrl() != null ? input.getUrl() : "");
            draft.setTarget(input.getTarget() != null ? input.getTarget() : "");
            draft.setResourceType(input.getResourceType());
            draft.setDisabled(input.getDisabled() != null ? input.getDisabled() : false);
            draft.setHidden(input.getHidden() != null ? input.getHidden() : false);
            draft.setAutoRefresh(input.getAutoRefresh() != null ? input.getAutoRefresh() : false);
            draft.setIcon(input.getIcon() != null ? input.getIcon() : "");
            draft.setRemark(input.getRemark() != null ? input.getRemark() : "");
        });
        
        SystemResource savedResource = resourceRepository.save(resource, SaveMode.UPDATE_ONLY);
        log.info("资源更新成功 - id: {}, name: {}, permission: {}, operator: {}",
                savedResource.id(), savedResource.name(), savedResource.permission(), getCurrentUserInfo());

        return savedResource;
    }

    /**
     * 删除资源
     */
    @Override
    @Transactional
    public boolean deleteResource(@NotNull Long resourceId) {
        log.debug("删除资源开始 - id: {}", resourceId);
        
        // 检查资源是否存在
        if (!resourceRepository.existsById(resourceId)) {
            log.warn("要删除的资源不存在 - id: {}", resourceId);
            return false;
        }
        
        // 检查是否有子资源
        List<SystemResource> children = findByParentId(resourceId, null);
        if (!children.isEmpty()) {
            throw AppErrors.CommonError.FORBIDDEN.toException("存在子资源，无法删除");
        }

        // 检查是否为内置资源
        if (resourceRepository.isBuiltinResource(resourceId)) {
            throw AppErrors.CommonError.FORBIDDEN.toException("内置资源无法删除");
        }
        
        try {
            resourceRepository.deleteById(resourceId);
            log.info("资源删除成功 - id: {}, operator: {}", resourceId, getCurrentUserInfo());
            return true;
        } catch (ErrorDetailException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除资源失败 - id: {}, operator: {}", resourceId, getCurrentUserInfo(), e);
            throw AppErrors.CommonError.INTERNAL_SERVER_ERROR.toException("删除资源失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询资源
     */
    @Override
    public Optional<SystemResource> findById(@NotNull Long resourceId, @Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findById(resourceId, fetcher);
    }

    /**
     * 根据ID查询资源（必须存在）
     */
    @Override
    public SystemResource getById(@NotNull Long resourceId, @Nullable Fetcher<SystemResource> fetcher) {
        return findById(resourceId, fetcher)
                .orElseThrow(() -> AppErrors.CommonError.NOT_FOUND.toException("资源不存在: " + resourceId));
    }

    /**
     * 根据权限编码查询资源
     */
    @Override
    public List<SystemResource> findByPermission(@NotNull String permission) {
        return resourceRepository.findByPermission(permission);
    }

    /**
     * 分页查询资源列表
     */
    @Override
    public Page<SystemResource> paginate(@Nullable String searchText,
                                        @Nullable SystemResource.ResourceType resourceType,
                                        @Nullable Boolean disabled,
                                        @NotNull Pageable pageable,
                                        @Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.paginate(searchText, resourceType, disabled, pageable, fetcher);
    }

    /**
     * 查询所有可用资源
     */
    @Override
    public List<SystemResource> findAllAvailable(@Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findAllAvailable(fetcher);
    }

    /**
     * 构建资源树结构
     */
    @Override
    public List<SystemResource> buildResourceTree(@Nullable Fetcher<SystemResource> fetcher) {
        // 获取所有资源
        List<SystemResource> allResources = resourceRepository.findResourceTree(fetcher);

        // 构建树状结构
        return buildTreeFromList(allResources);
    }

    /**
     * 从平铺列表构建树状结构
     */
    private List<SystemResource> buildTreeFromList(List<SystemResource> allResources) {
        Map<Long, List<SystemResource>> parentChildMap = new HashMap<>();
        List<SystemResource> rootResources = new ArrayList<>();

        // 按父级ID分组
        for (SystemResource resource : allResources) {
            if (resource.parentId() == null) {
                rootResources.add(resource);
            } else {
                parentChildMap.computeIfAbsent(resource.parentId(), k -> new ArrayList<>()).add(resource);
            }
        }

        return rootResources;
    }

    /**
     * 根据父级ID查询子资源
     */
    @Override
    public List<SystemResource> findByParentId(@Nullable Long parentId, @Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findByParentId(parentId, fetcher);
    }

    /**
     * 查询菜单类型的资源
     */
    @Override
    public List<SystemResource> findMenuResources(@Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findMenuResources(fetcher);
    }

    /**
     * 根据资源类型查询资源
     */
    @Override
    public List<SystemResource> findByResourceType(@NotNull SystemResource.ResourceType resourceType, 
                                                  @Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findByResourceType(resourceType, fetcher);
    }

    /**
     * 检查权限编码是否存在
     */
    @Override
    public boolean existsByPermission(@NotNull String permission) {
        return !resourceRepository.findByPermission(permission).isEmpty();
    }

    /**
     * 检查权限编码是否存在（排除指定ID）
     */
    @Override
    public boolean existsByPermissionAndIdNot(@NotNull String permission, @Nullable Long excludeId) {
        List<SystemResource> resources = resourceRepository.findByPermission(permission);
        if (excludeId == null) {
            return !resources.isEmpty();
        }
        return resources.stream().anyMatch(resource -> !excludeId.equals(resource.id()));
    }

    /**
     * 根据用户ID查询用户拥有的资源权限
     */
    @Override
    public List<SystemResource> findUserResources(@NotNull Long userId, @Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findUserResources(userId, fetcher);
    }

    /**
     * 根据用户ID查询菜单类型的资源
     */
    @Override
    public List<SystemResource> findUserMenuResources(@NotNull Long userId, @Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findUserMenuResources(userId, fetcher);
    }

    /**
     * 检查用户是否拥有指定权限
     */
    @Override
    public boolean hasPermission(@NotNull Long userId, @NotNull String permission) {
        return resourceRepository.hasPermission(userId, permission);
    }

    /**
     * 检查用户是否拥有任意一个权限
     */
    @Override
    public boolean hasAnyPermission(@NotNull Long userId, @NotNull List<String> permissions) {

        if (permissions.isEmpty()) {
            return false;
        }
        
        return permissions.stream().anyMatch(permission -> hasPermission(userId, permission));
    }

    /**
     * 检查用户是否拥有所有权限
     */
    @Override
    public boolean hasAllPermissions(@NotNull Long userId, @NotNull List<String> permissions) {
        if (permissions.isEmpty()) {
            return true;
        }
        
        return permissions.stream().allMatch(permission -> hasPermission(userId, permission));
    }



    /**
     * 处理资源权限编码
     * 对于第三方资源，自动生成权限编码；对于系统资源，使用用户输入的权限编码
     */
    private String processPermissionForResource(@NotNull CreateSystemResourceInput input) {
        // 如果用户已经提供了权限编码，直接使用
        if (input.getPermission() != null && !input.getPermission().trim().isEmpty()) {
            return input.getPermission().trim();
        }

        // 判断是否为第三方资源（外部链接）
        if (isExternalResource(input)) {
            // 为第三方资源自动生成权限编码
            return generateExternalResourcePermission(input.getName());
        }

        // 对于目录类型的资源，权限可以为空
        if (input.getResourceType() == SystemResource.ResourceType.FOLDER) {
            return "";
        }

        // 其他情况返回空字符串，表示无需权限控制
        return "";
    }

    /**
     * 判断是否为第三方外部资源
     */
    private boolean isExternalResource(@NotNull CreateSystemResourceInput input) {
        String url = input.getUrl();
        return url != null && (url.startsWith("http://") || url.startsWith("https://"));
    }

    /**
     * 为第三方资源生成权限编码
     */
    private String generateExternalResourcePermission(@NotNull String resourceName) {
        // 生成格式：external:resourceName
        String cleanName = resourceName.replaceAll("[^a-zA-Z0-9_]", "_").toLowerCase();
        return "external:" + cleanName;
    }

    /**
     * 将权限分配给当前用户的所有角色
     */
    private void assignPermissionToCurrentUserRoles(@NotNull Long resourceId, @NotNull String permission) {
        try {
            // 获取当前用户
            Long currentUserId = AuthContext.currentUser().id();

            // 获取当前用户的所有角色
            List<SystemRole> userRoles = roleRepository.findUserRoles(currentUserId);

            if (!userRoles.isEmpty()) {
                // 为每个角色分配这个资源权限
                for (SystemRole role : userRoles) {
                    try {
                        roleRepository.assignResources(role.id(), List.of(resourceId));
                        log.info("为角色分配第三方资源权限成功: roleId={}, roleName={}, resourceId={}, permission={}",
                                role.id(), role.name(), resourceId, permission);
                    } catch (Exception e) {
                        log.warn("为角色分配第三方资源权限失败: roleId={}, roleName={}, resourceId={}, permission={}",
                                role.id(), role.name(), resourceId, permission, e);
                    }
                }
            } else {
                log.warn("当前用户没有角色，无法自动分配第三方资源权限: userId={}, resourceId={}, permission={}",
                        currentUserId, resourceId, permission);
            }
        } catch (Exception e) {
            log.error("自动分配第三方资源权限失败: resourceId={}, permission={}", resourceId, permission, e);
        }
    }

    private String getCurrentUserInfo() {
        try {
            return AuthContext.currentUser().toStringInfo();
        } catch (Exception e) {
            return "系统";
        }
    }
}
